# Spring Boot 基础配置
spring:
  application:
    name: ai-code-helper
  profiles:
    # 默认激活 postgresql 数据库配置
    active: postgresql,gemini,bigmodel,modelscope
  servlet:
    multipart:
      # 设置文件上传大小限制，避免默认 1MB 过小导致 MaxUploadSizeExceededException
      # 可通过环境变量覆盖：UPLOAD_MAX_FILE_SIZE / UPLOAD_MAX_REQUEST_SIZE
      enabled: true
      max-file-size: ${UPLOAD_MAX_FILE_SIZE:10MB}
      max-request-size: ${UPLOAD_MAX_REQUEST_SIZE:12MB}

# 服务器配置
server:
  port: ${SERVER_PORT:8081}
  servlet:
    context-path: ${SERVER_CONTEXT_PATH:/api}

# 聊天记忆基础配置
chat:
  memory:
    # 记忆类型: message（消息窗口） 或 token（令牌窗口）
    type: ${CHAT_MEMORY_TYPE:message}
    # 最大消息数量（消息窗口模式）
    max-messages: ${CHAT_MEMORY_MAX_MESSAGES:20}
    # 最大令牌数量（令牌窗口模式）
    max-tokens: ${CHAT_MEMORY_MAX_TOKENS:1000}
    # 调试模式
    debug-mode: ${CHAT_MEMORY_DEBUG:true}
    # 缓存配置
    cache:
      enabled: ${CHAT_MEMORY_CACHE_ENABLED:true}
      expire-seconds: ${CHAT_MEMORY_CACHE_EXPIRE:30}
      max-size: ${CHAT_MEMORY_CACHE_MAX_SIZE:1000}
      enable-stats: ${CHAT_MEMORY_CACHE_STATS:false}

# AI 聊天基础配置
ai:

  catalog:
    # UseCase到Provider的路由配置
    text: ${AI_CATALOG_TEXT:bigmodel}
    multimodal: ${AI_CATALOG_MULTIMODAL:bigmodel}
    rag: ${AI_CATALOG_RAG:bigmodel}
    dictionary: ${AI_CATALOG_DICTIONARY:bigmodel}
    pro: ${AI_CATALOG_PRO:bigmodel}

    # 各提供商模型槽位配置
    gemini:
      text: ${AI_GEMINI_TEXT_SLOT:lite}
      rag: ${AI_GEMINI_RAG_SLOT:rag}
      dictionary: ${AI_GEMINI_DICTIONARY_SLOT:dictionary}
      pro: ${AI_GEMINI_PRO_SLOT:pro}

    bigmodel:
      text: ${AI_BIGMODEL_TEXT_SLOT:lite}
      multimodal: ${AI_BIGMODEL_MULTIMODAL_SLOT:multimodal}
      rag: ${AI_BIGMODEL_RAG_SLOT:rag}
      dictionary: ${AI_BIGMODEL_DICTIONARY_SLOT:dictionary}
      pro: ${AI_BIGMODEL_PRO_SLOT:pro}

    modelscope:
      text: ${AI_MODELSCOPE_TEXT_SLOT:lite}
      multimodal: ${AI_MODELSCOPE_MULTIMODAL_SLOT:multimodal}
      rag: ${AI_MODELSCOPE_RAG_SLOT:rag}
      dictionary: ${AI_MODELSCOPE_DICTIONARY_SLOT:dictionary}
      pro: ${AI_MODELSCOPE_PRO_SLOT:pro}
  
  chat:
    image:
      # 向大模型传递图片的方式：url | base64 | auto
      delivery: ${AI_CHAT_IMAGE_DELIVERY:url}
      # 公网访问基础URL（用于将相对图片路径拼成可被模型访问的完整URL）
      # 若未设置环境变量，则默认使用你的域名 https://www.thinkyai.net
      public-base-url: ${AI_CHAT_IMAGE_PUBLIC_BASE_URL:https://www.thinkyai.net}
      # 图片大小限制
      max-bytes: ${AI_CHAT_IMAGE_MAX_BYTES:120000}
      max-width: ${AI_CHAT_IMAGE_MAX_WIDTH:1024}
      max-height: ${AI_CHAT_IMAGE_MAX_HEIGHT:1024}
    moderation:
      # 审核失败时策略：error | fallback_text
      on-fail: ${AI_CHAT_MODERATION_ON_FAIL:fallback_text}

# ThinkyAI 结构化输出配置
thinky:
  ai:
    # ChatModel 配置
    chat-model:
      provider: ${THINKY_AI_PROVIDER:bigmodel}  # 提供商：bigmodel, modelscope, gemini
      api-key: ${THINKY_AI_API_KEY:}  # 留空则使用默认值
      model-name: ${THINKY_AI_MODEL:glm-4-flash}  # 模型名称
      base-url: ${THINKY_AI_BASE_URL:}  # 留空则使用默认值
      temperature: 0.3  # 结构化输出使用较低温度
      max-tokens: 2000
    
    # 结构化输出配置
    structured-output:
      enabled: true
      trigger:
        completion-keywords: ["做完了", "我的答案", "对不对", "最终答案", "结果是"]
        diagnosis-threshold: 2  # 连续错误次数触发诊断
        milestone-problems: 5    # 里程碑题目数
        milestone-time: 30      # 里程碑时间(分钟)
      model:
        temperature: 0.3  # 结构化输出使用较低温度
        max-tokens: 2000
        response-format: json_object  # 启用JSON格式输出
      knowledge-graph:
        enabled: false  # 是否启用外部知识图谱
        endpoint: ""    # 外部知识图谱服务端点

# LangChain4j 配置增强
langchain4j:
  google-ai-gemini:
    chat-model:
      response-format: json_object  # 启用Gemini的JSON输出
  open-ai:
    chat-model:
      response-format: json_object  # 启用OpenAI的JSON输出

# RAG 配置
rag:
  # 数学知识库配置
  math-knowledge:
    enabled: ${RAG_MATH_KNOWLEDGE_ENABLED:true}
    source-path: ${RAG_MATH_KNOWLEDGE_SOURCE_PATH:data}
  
  # JSON文件处理配置
  json:
    enabled: ${RAG_JSON_ENABLED:true}
    source-paths:
      - ${RAG_JSON_SOURCE_PATH_1:data/cambridge/2025-06-28/output_json_standalone}
      - ${RAG_JSON_SOURCE_PATH_2:data/myelt/2025-06-26/metadata_json}
    include-patterns:
      - "**/*.json"
    exclude-patterns:
      - "**/temp/**"
      - "**/.DS_Store"
      - "**/node_modules/**"
    max-file-size: ${RAG_JSON_MAX_FILE_SIZE:10485760}
    processing-timeout: ${RAG_JSON_PROCESSING_TIMEOUT:300}
    parallel-processing: ${RAG_JSON_PARALLEL_PROCESSING:true}
    thread-pool-size: ${RAG_JSON_THREAD_POOL_SIZE:4}


# MCP 配置
bigmodel:
  api-key: ${BIGMODEL_API_KEY:a08cf598f1d6486f8e19f6783966ebcd.eubFlWtgOU1X8nrz}

# 日志配置
logging:
  level:
    # AI相关日志
    com.hujun.aicodehelper.ai: ${LOG_LEVEL_AI:DEBUG}
    com.hujun.aicodehelper.ai.listener: ${LOG_LEVEL_AI_LISTENER:DEBUG}
    com.hujun.aicodehelper.ai.strategy: ${LOG_LEVEL_AI_STRATEGY:DEBUG}
    com.hujun.aicodehelper.chat: ${LOG_LEVEL_CHAT:DEBUG}
    # 数据库日志
    org.hibernate.SQL: ${LOG_LEVEL_HIBERNATE_SQL:WARN}
    org.hibernate.type.descriptor.sql.BasicBinder: ${LOG_LEVEL_HIBERNATE_BINDER:WARN}
    org.springframework.transaction: ${LOG_LEVEL_SPRING_TRANSACTION:WARN}
    org.springframework.orm.jpa: ${LOG_LEVEL_SPRING_JPA:WARN}
    # LangChain4j 日志
    dev.langchain4j: ${LOG_LEVEL_LANGCHAIN4J:DEBUG}
