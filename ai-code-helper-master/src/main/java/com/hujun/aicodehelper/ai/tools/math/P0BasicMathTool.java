package com.hujun.aicodehelper.ai.tools.math;

import org.springframework.stereotype.Component;

import dev.langchain4j.agent.tool.P;
import dev.langchain4j.agent.tool.Tool;
import lombok.extern.slf4j.Slf4j;

/**
 * P0 级别数学工具：基础代数运算
 * 涵盖：一元方程/方程组、因式分解、化简、展开、分式运算、不等式求解
 */
@Slf4j
@Component
public class P0BasicMathTool extends BaseMathTool {

    /**
     * 求解一元方程
     * 例如：x^2 + 2*x - 3 = 0
     */
    @Tool(name = "solveEquation", value = """
            求解一元方程。支持线性方程、二次方程及简单高次方程。
            输入格式：方程式（使用 = 连接等号两边）
            示例：x^2 + 2*x - 3 = 0, 2*x + 5 = 11
            """)
    public String solveEquation(@P("要求解的一元方程，如 'x^2 + 2*x - 3 = 0'") String equation) {
        log.info("🔍 求解一元方程: {}", equation);

        if (!isSafeExpression(equation)) {
            return "❌ 表达式包含不安全的操作";
        }

        try {
            // 检查是否为引导式学习模式
            if (isGuidedLearningMode()) {
                log.info("🎓 引导式学习模式：不执行实际计算，返回引导提示");
                return formatEquationSolution(equation, null);
            }

            // 转换为 Symja 的 Solve 函数格式
            String symjaExpression = String.format("Solve(%s, x)", equation);
            MathResult result = evaluateExpression(symjaExpression);

            if (result.isSuccess()) {
                return formatEquationSolution(equation, result.getResult());
            } else {
                return result.getError();
            }
        } catch (Exception e) {
            log.error("求解方程时发生错误", e);
            return "❌ 方程求解失败：" + e.getMessage();
        }
    }

    /**
     * 求解方程组
     * 例如：{x + y = 5, x - y = 1}
     */
    @Tool(name = "solveSystem", value = """
            求解线性方程组。支持2-3个未知数的线性方程组。
            输入格式：用大括号包围方程组，方程间用逗号分隔
            示例：{x + y = 5, x - y = 1}, {2*x + y = 7, x - y = 1}
            """)
    public String solveSystem(@P("方程组，如 '{x + y = 5, x - y = 1}'") String system) {
        log.info("🔍 求解方程组: {}", system);

        if (!isSafeExpression(system)) {
            return "❌ 表达式包含不安全的操作";
        }

        try {
            // 检查是否为引导式学习模式
            if (isGuidedLearningMode()) {
                log.info("🎓 引导式学习模式：不执行实际计算，返回引导提示");
                return formatSystemSolution(system, null);
            }

            // 转换为 Symja 的 Solve 函数格式，自动检测变量
            String symjaExpression = String.format("Solve(%s, {x, y})", system);
            MathResult result = evaluateExpression(symjaExpression);

            if (result.isSuccess()) {
                return formatSystemSolution(system, result.getResult());
            } else {
                // 尝试三变量求解
                symjaExpression = String.format("Solve(%s, {x, y, z})", system);
                result = evaluateExpression(symjaExpression);
                if (result.isSuccess()) {
                    return formatSystemSolution(system, result.getResult());
                }
                return result.getError();
            }
        } catch (Exception e) {
            log.error("求解方程组时发生错误", e);
            return "❌ 方程组求解失败：" + e.getMessage();
        }
    }

    /**
     * 因式分解
     * 例如：x^2 + 5*x + 6
     */
    @Tool(name = "factorExpression", value = """
            对多项式进行因式分解。支持常见的二次式、三次式等多项式因式分解。
            输入格式：多项式表达式
            示例：x^2 + 5*x + 6, x^3 - 8, a^2 - b^2
            """)
    public String factorExpression(@P("需要因式分解的多项式，如 'x^2 + 5*x + 6'") String expression) {
        log.info("🧮 因式分解: {}", expression);

        if (!isSafeExpression(expression)) {
            return "❌ 表达式包含不安全的操作";
        }

        try {
            // 检查是否为引导式学习模式
            if (isGuidedLearningMode()) {
                log.info("🎓 引导式学习模式：不执行实际计算，返回引导提示");
                return formatFactorResult(expression, null);
            }

            String symjaExpression = String.format("Factor(%s)", expression);
            MathResult result = evaluateExpression(symjaExpression);

            if (result.isSuccess()) {
                return formatFactorResult(expression, result.getResult());
            } else {
                return result.getError();
            }
        } catch (Exception e) {
            log.error("因式分解时发生错误", e);
            return "❌ 因式分解失败：" + e.getMessage();
        }
    }

    /**
     * 表达式化简
     * 例如：(x^2 + 2*x + 1)/(x + 1)
     */
    @Tool(name = "simplifyExpression", value = """
            化简数学表达式。包括分式化简、根式化简、三角函数化简等。
            输入格式：数学表达式
            示例：(x^2 + 2*x + 1)/(x + 1), sqrt(8), sin(x)^2 + cos(x)^2
            """)
    public String simplifyExpression(@P("需要化简的数学表达式") String expression) {
        log.info("✨ 化简表达式: {}", expression);

        if (!isSafeExpression(expression)) {
            return "❌ 表达式包含不安全的操作";
        }

        try {
            // 检查是否为引导式学习模式
            if (isGuidedLearningMode()) {
                log.info("🎓 引导式学习模式：不执行实际计算，返回引导提示");
                return formatSimplificationResult(expression, null);
            }

            String symjaExpression = String.format("Simplify(%s)", expression);
            MathResult result = evaluateExpression(symjaExpression);

            if (result.isSuccess()) {
                return formatSimplificationResult(expression, result.getResult());
            } else {
                return result.getError();
            }
        } catch (Exception e) {
            log.error("化简表达式时发生错误", e);
            return "❌ 表达式化简失败：" + e.getMessage();
        }
    }

    /**
     * 表达式展开
     * 例如：(x + 1)^2, (a + b)(c + d)
     */
    @Tool(name = "expandExpression", value = """
            展开数学表达式。包括多项式展开、幂次展开等。
            输入格式：包含括号或幂次的数学表达式
            示例：(x + 1)^2, (a + b)(c + d), (x + y + z)^3
            """)
    public String expandExpression(@P("需要展开的数学表达式，如 '(x + 1)^2'") String expression) {
        log.info("📐 展开表达式: {}", expression);

        if (!isSafeExpression(expression)) {
            return "❌ 表达式包含不安全的操作";
        }

        try {
            // 检查是否为引导式学习模式
            if (isGuidedLearningMode()) {
                log.info("🎓 引导式学习模式：不执行实际计算，返回引导提示");
                return formatExpansionResult(expression, null);
            }

            String symjaExpression = String.format("Expand(%s)", expression);
            MathResult result = evaluateExpression(symjaExpression);

            if (result.isSuccess()) {
                return formatExpansionResult(expression, result.getResult());
            } else {
                return result.getError();
            }
        } catch (Exception e) {
            log.error("展开表达式时发生错误", e);
            return "❌ 表达式展开失败：" + e.getMessage();
        }
    }

    /**
     * 求解一次不等式
     * 例如：2*x + 3 > 7, x^2 - 4 >= 0
     */
    @Tool(name = "solveInequality", value = """
            求解不等式。支持一次不等式和简单的二次不等式。
            输入格式：不等式（使用 >, <, >=, <= 符号）
            示例：2*x + 3 > 7, x^2 - 4 >= 0, |x - 1| < 3
            """)
    public String solveInequality(@P("不等式，如 '2*x + 3 > 7'") String inequality) {
        log.info("📊 求解不等式: {}", inequality);

        if (!isSafeExpression(inequality)) {
            return "❌ 表达式包含不安全的操作";
        }

        try {
            // 检查是否为引导式学习模式
            if (isGuidedLearningMode()) {
                log.info("🎓 引导式学习模式：不执行实际计算，返回引导提示");
                return formatInequalityResult(inequality, null);
            }

            String symjaExpression = String.format("Solve(%s, x, Reals)", inequality);
            MathResult result = evaluateExpression(symjaExpression);

            if (result.isSuccess()) {
                return formatInequalityResult(inequality, result.getResult());
            } else {
                return result.getError();
            }
        } catch (Exception e) {
            log.error("求解不等式时发生错误", e);
            return "❌ 不等式求解失败：" + e.getMessage();
        }
    }

    /**
     * 表达式求值
     * 例如：在 x=2 时计算 x^2 + 3*x + 1 的值
     */
    @Tool(name = "evaluateAt", value = """
            在指定变量值处计算表达式的数值。
            输入格式：表达式;变量=值 或 表达式;{变量1=值1,变量2=值2}
            示例：x^2 + 3*x + 1;x=2, a^2 + b^2;{a=3,b=4}
            """)
    public String evaluateAt(@P("表达式和变量赋值，如 'x^2 + 3*x + 1;x=2'") String expressionAndValues) {
        log.info("🎯 表达式求值: {}", expressionAndValues);

        if (!isSafeExpression(expressionAndValues)) {
            return "❌ 表达式包含不安全的操作";
        }

        try {
            String[] parts = expressionAndValues.split(";");
            if (parts.length != 2) {
                return "❌ 格式错误，请使用 '表达式;变量=值' 的格式";
            }

            String expression = parts[0].trim();
            String substitution = parts[1].trim();

            // 检查是否为引导式学习模式
            if (isGuidedLearningMode()) {
                log.info("🎓 引导式学习模式：不执行实际计算，返回引导提示");
                return formatEvaluationResult(expression, substitution, null);
            }

            // 处理单变量赋值
            if (substitution.contains("=") && !substitution.contains("{")) {
                substitution = "{" + substitution + "}";
            }

            String symjaExpression = String.format("ReplaceAll(%s, %s)", expression, substitution);
            MathResult result = evaluateExpression(symjaExpression);

            if (result.isSuccess()) {
                // 尝试数值化计算
                String numericExpression = String.format("N(%s)", result.getResult());
                MathResult numericResult = evaluateExpression(numericExpression);

                return formatEvaluationResult(expression, substitution,
                        numericResult.isSuccess() ? numericResult.getResult() : result.getResult());
            } else {
                return result.getError();
            }
        } catch (Exception e) {
            log.error("表达式求值时发生错误", e);
            return "❌ 表达式求值失败：" + e.getMessage();
        }
    }

    // === 格式化输出方法 ===

    private String formatEquationSolution(String equation, String solution) {
        // 注意：solution参数保留用于未来可能的引导式提示，但当前不直接显示答案
        return String.format("""
                📝 **一元方程求解引导**
                原方程：%s

                🤔 **思考提示**：
                1. 这个方程的类型是什么？（一次方程、二次方程等）
                2. 你会选择什么方法来求解？
                3. 第一步应该如何处理？

                💡 **解题思路**：
                - 观察方程的结构和特点
                - 选择合适的解法（移项、因式分解、公式法等）
                - 逐步化简，直到得到 x 的值
                - 记得验证你的答案！

                ⚠️ **常见错误提醒**：
                - 移项时要注意符号变化
                - 两边同时除以某个数时，要确保该数不为零

                🎯 **下一步**：请尝试按照上述思路求解，我会在过程中给你提示！
                """, equation);
    }

    private String formatSystemSolution(String system, String solution) {
        // 注意：solution参数保留用于未来可能的引导式提示，但当前不直接显示答案
        return String.format("""
                📝 **方程组求解引导**
                原方程组：%s

                🤔 **思考提示**：
                1. 这个方程组有几个未知数？几个方程？
                2. 你会选择什么方法求解？（代入法、消元法、矩阵法）
                3. 哪个方程看起来更容易处理？

                💡 **解题思路**：
                - 观察方程组的结构特点
                - 选择合适的消元策略
                - 逐步消去变量，化简为更简单的方程
                - 求出一个变量后，代入求其他变量
                - 验证所有解是否满足原方程组

                ⚠️ **常见错误提醒**：
                - 消元时要保持等式两边平衡
                - 代入时要仔细计算，避免符号错误
                - 记得检验解的正确性

                🎯 **下一步**：请选择一种方法开始求解，我会引导你完成每一步！
                """, system);
    }

    private String formatFactorResult(String original, String factored) {
        // 注意：factored参数保留用于未来可能的引导式提示，但当前不直接显示答案
        if (original.equals(factored)) {
            return String.format("""
                    📝 **因式分解引导**
                    原表达式：%s

                    🤔 **思考提示**：
                    1. 这个表达式的次数是多少？
                    2. 有没有公因子可以提取？
                    3. 是否符合某种特殊的因式分解公式？

                    💡 **解题思路**：
                    - 先检查是否有公因子
                    - 观察是否为完全平方式、平方差等特殊形式
                    - 对于二次式，尝试十字相乘法
                    - 对于高次式，考虑分组分解

                    ⚠️ **提示**：这个表达式可能已经是最简形式，或者需要特殊技巧才能分解。

                    🎯 **下一步**：请仔细观察表达式的结构，尝试找到分解的突破口！
                    """, original);
        } else {
            return String.format("""
                    📝 **因式分解引导**
                    原表达式：%s

                    🤔 **思考提示**：
                    1. 观察这个表达式的结构特点
                    2. 你会选择什么分解方法？
                    3. 第一步应该提取什么？

                    💡 **解题思路**：
                    - 先提取公因子（如果有的话）
                    - 识别特殊的分解公式（平方差、完全平方等）
                    - 对于二次三项式，使用十字相乘法
                    - 分解后记得验证结果

                    ⚠️ **常见错误提醒**：
                    - 不要忘记提取公因子
                    - 注意符号的处理
                    - 分解完成后要检查是否还能继续分解

                    🎯 **下一步**：请按照上述思路尝试分解，我会在过程中给你提示！
                    """, original);
        }
    }

    private String formatSimplificationResult(String original, String simplified) {
        // 注意：simplified参数保留用于未来可能的引导式提示，但当前不直接显示答案
        if (original.equals(simplified)) {
            return String.format("""
                    📝 **表达式化简引导**
                    原表达式：%s

                    🤔 **思考提示**：
                    1. 这个表达式还能进一步化简吗？
                    2. 有没有同类项可以合并？
                    3. 分数形式是否已经是最简？

                    💡 **化简策略**：
                    - 合并同类项
                    - 约分（对于分式）
                    - 提取公因子
                    - 化简根式（如果有的话）

                    ✅ **结论**：这个表达式已经是最简形式了！

                    🎯 **验证**：请检查是否还有其他化简的可能性。
                    """, original);
        } else {
            return String.format("""
                    📝 **表达式化简引导**
                    原表达式：%s

                    🤔 **思考提示**：
                    1. 观察表达式的结构，有什么特点？
                    2. 哪些项可以合并？
                    3. 是否有公因子可以提取？

                    💡 **化简步骤**：
                    - 识别同类项并合并
                    - 对分式进行约分
                    - 提取公因子
                    - 整理表达式的形式

                    ⚠️ **注意事项**：
                    - 化简过程中要保持等价性
                    - 注意符号的正确处理
                    - 分母不能为零

                    🎯 **下一步**：请尝试按照上述步骤化简表达式！
                    """, original);
        }
    }

    private String formatExpansionResult(String original, String expanded) {
        // 注意：expanded参数保留用于未来可能的引导式提示，但当前不直接显示答案
        return String.format("""
                📝 **表达式展开引导**
                原表达式：%s

                🤔 **思考提示**：
                1. 这个表达式包含什么类型的乘法？
                2. 你会使用什么展开法则？
                3. 展开时需要注意什么？

                💡 **展开策略**：
                - 识别乘法类型（单项式×多项式、多项式×多项式等）
                - 应用分配律：a(b+c) = ab + ac
                - 使用特殊公式：(a+b)² = a² + 2ab + b²
                - 注意符号的正确处理

                ⚠️ **常见错误提醒**：
                - 不要遗漏任何一项
                - 注意符号变化
                - 展开后要合并同类项

                🎯 **下一步**：请尝试按照分配律逐步展开表达式！
                """, original);
    }

    private String formatInequalityResult(String inequality, String solution) {
        // 注意：solution参数保留用于未来可能的引导式提示，但当前不直接显示答案
        return String.format("""
                📝 **不等式求解引导**
                原不等式：%s

                🤔 **思考提示**：
                1. 这是什么类型的不等式？（一次、二次、分式等）
                2. 求解不等式和求解方程有什么区别？
                3. 什么时候需要改变不等号方向？

                💡 **解题思路**：
                - 将不等式化为标准形式
                - 找到关键点（零点、断点）
                - 用数轴标根法或穿根法
                - 确定解集的区间表示

                ⚠️ **重要提醒**：
                - 两边同乘/除负数时，不等号要变向
                - 注意定义域的限制
                - 解集要用区间或集合表示

                🎯 **下一步**：请尝试分析不等式的结构，选择合适的求解方法！
                """, inequality);
    }

    private String formatEvaluationResult(String expression, String substitution, String value) {
        // 注意：value参数保留用于未来可能的引导式提示，但当前不直接显示答案
        return String.format("""
                📝 **表达式求值引导**
                原表达式：%s
                代入条件：%s

                🤔 **思考提示**：
                1. 代入数值前，先观察表达式的结构
                2. 按照什么顺序进行计算？
                3. 需要注意哪些运算优先级？

                💡 **求值步骤**：
                - 将给定的数值代入表达式
                - 按照运算优先级进行计算
                - 先算括号内的，再算乘除，最后算加减
                - 仔细处理符号和小数

                ⚠️ **注意事项**：
                - 代入时要用括号包围负数
                - 注意运算顺序
                - 检查计算过程中的每一步

                🎯 **下一步**：请按照上述步骤逐步计算表达式的值！
                """, expression, substitution);
    }
}
